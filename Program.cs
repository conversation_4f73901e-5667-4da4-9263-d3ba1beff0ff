using Spectre.Console;

namespace Sage;

public class Program
{
    private static readonly HttpClient httpClient = new();

    public static async Task Main()
    {
        // Example usage
        var urls = new[]
        {
            "https://httpbin.org/delay/1",
            "https://httpbin.org/delay/2",
            "https://httpbin.org/delay/1"
        };

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

        try
        {
            var results = await ConcurrentDownloader(cts.Token, urls);

            AnsiConsole.MarkupLine("[green]Download completed successfully![/]");
            AnsiConsole.MarkupLine($"[blue]Downloaded {results.Count} files[/]");
        }
        catch (OperationCanceledException)
        {
            AnsiConsole.MarkupLine("[red]Download operation was cancelled[/]");
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]Error: {ex.Message}[/]");
        }
    }

    /// <summary>
    /// Downloads multiple URLs concurrently using async/await pattern
    /// </summary>
    /// <param name="cancellationToken">Cancellation token to cancel the operation</param>
    /// <param name="urls">Array of URLs to download</param>
    /// <returns>Dictionary containing URL as key and downloaded content as value</returns>
    public static async Task<Dictionary<string, string>> ConcurrentDownloader(
        CancellationToken cancellationToken,
        params string[] urls)
    {
        if (urls == null || urls.Length == 0)
        {
            return [];
        }

        // Create download tasks for all URLs
        var downloadTasks = urls.Select(url => DownloadSingleUrl(url, cancellationToken)).ToArray();

        // Wait for all downloads to complete
        var results = await Task.WhenAll(downloadTasks);

        // Create result dictionary
        var resultDictionary = new Dictionary<string, string>();
        for (int i = 0; i < urls.Length; i++)
        {
            resultDictionary[urls[i]] = results[i];
        }

        return resultDictionary;
    }

    /// <summary>
    /// Downloads content from a single URL
    /// </summary>
    /// <param name="url">URL to download</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Downloaded content as string</returns>
    private static async Task<string> DownloadSingleUrl(string url, CancellationToken cancellationToken)
    {
        try
        {
            AnsiConsole.MarkupLine($"[yellow]Starting download: {url}[/]");

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            AnsiConsole.MarkupLine($"[green]Completed download: {url}[/]");
            return content;
        }
        catch (HttpRequestException ex)
        {
            AnsiConsole.MarkupLine($"[red]HTTP error downloading {url}: {ex.Message}[/]");
            throw;
        }
        catch (TaskCanceledException ex) when (ex.CancellationToken == cancellationToken)
        {
            AnsiConsole.MarkupLine($"[orange1]Download cancelled: {url}[/]");
            throw new OperationCanceledException($"Download of {url} was cancelled", ex, cancellationToken);
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]Error downloading {url}: {ex.Message}[/]");
            throw;
        }
    }
}